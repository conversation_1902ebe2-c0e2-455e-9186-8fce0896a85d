/**
 * 角色卡相关类型定义
 */

// 角色性别类型（用于模板生成）
export type CharacterGender = '男' | '女' | '无';

// 角色卡数据结构（纯文本模式）
export interface Character {
  id: string;
  name: string;
  content: string; // 角色的所有信息以纯文本形式保存
  workId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 角色卡模态窗口属性
export interface CharacterCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  workId: number;
}

// 角色卡表单数据（用于模板生成）
export interface CharacterFormData {
  name: string;
  gender: CharacterGender;
  personality: string;
  background: string;
}
