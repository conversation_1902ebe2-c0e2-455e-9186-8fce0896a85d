/**
 * 角色仓库
 */
import { Character } from '@/types/character';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { CHARACTERS } = DB_CONFIG.STORES.MAIN;

/**
 * 添加角色
 * @param character 角色数据
 * @returns 添加后的角色
 */
export const addCharacter = async (character: Character): Promise<Character> => {
  console.log('添加角色到数据库:', character);
  return dbOperations.add<Character>(MAIN, CHARACTERS, character);
};

/**
 * 获取所有角色
 * @returns 所有角色
 */
export const getAllCharacters = async (): Promise<Character[]> => {
  return dbOperations.getAll<Character>(MAIN, CHARACTERS);
};

/**
 * 根据作品ID获取角色
 * @param workId 作品ID
 * @returns 该作品的所有角色
 */
export const getCharactersByWorkId = async (workId: number): Promise<Character[]> => {
  const allCharacters = await getAllCharacters();
  return allCharacters.filter(character => character.workId === workId)
    .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
};

/**
 * 根据ID获取角色
 * @param id 角色ID
 * @returns 角色
 */
export const getCharacterById = async (id: string): Promise<Character | null> => {
  try {
    return await dbOperations.get<Character>(MAIN, CHARACTERS, id);
  } catch (error) {
    console.error('获取角色失败:', error);
    return null;
  }
};

/**
 * 更新角色
 * @param character 角色数据
 * @returns 更新后的角色
 */
export const updateCharacter = async (character: Character): Promise<Character> => {
  if (!character.id) throw new Error('Character ID is required');
  return dbOperations.update<Character>(MAIN, CHARACTERS, character);
};

/**
 * 删除角色
 * @param id 角色ID
 */
export const deleteCharacter = async (id: string): Promise<void> => {
  return dbOperations.remove(MAIN, CHARACTERS, id);
};

/**
 * 根据性别获取角色（从content中解析）
 * @param workId 作品ID
 * @param gender 性别
 * @returns 该性别的所有角色
 */
export const getCharactersByGender = async (workId: number, gender: string): Promise<Character[]> => {
  const workCharacters = await getCharactersByWorkId(workId);
  return workCharacters.filter(character => {
    // 从content中解析性别信息
    const lines = character.content.split('\n');
    const genderLine = lines.find(line => line.startsWith('性别：'));
    const characterGender = genderLine ? genderLine.replace('性别：', '').trim() : '';
    return characterGender === gender;
  });
};
