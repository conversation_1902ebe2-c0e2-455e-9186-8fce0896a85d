/**
 * 角色卡模态窗口组件
 */
import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';
import { Character, CharacterCardModalProps, CharacterFormData, CharacterGender } from '@/types/character';

/**
 * 角色卡模态窗口组件
 */
export const CharacterCardModal: React.FC<CharacterCardModalProps> = ({
  isOpen,
  onClose,
  workId,
  onDataChange
}) => {
  // 状态管理
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [formData, setFormData] = useState<CharacterFormData>({
    name: '',
    gender: '无',
    personality: '',
    background: ''
  });

  // 加载角色数据（使用IndexedDB）
  const loadCharacters = async () => {
    try {
      const { getCharactersByWorkId } = await import('@/data');
      const workCharacters = await getCharactersByWorkId(workId);
      setCharacters(workCharacters);
    } catch (error) {
      console.error('加载角色数据失败:', error);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    if (isOpen) {
      loadCharacters();
    }
  }, [isOpen, workId]);

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  // 创建新角色
  const createNewCharacter = async () => {
    const newCharacter: Character = {
      id: generateId(),
      name: '新角色',
      content: '',
      workId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      const { addCharacter } = await import('@/data');
      await addCharacter(newCharacter);
      const updatedCharacters = [...characters, newCharacter];
      setCharacters(updatedCharacters);
      setSelectedCharacter(newCharacter);
      setFormData({
        name: newCharacter.name,
        gender: '无',
        personality: '',
        background: ''
      });
      // 通知数据变更
      onDataChange?.();
    } catch (error) {
      console.error('创建角色失败:', error);
    }
  };

  // 选择角色
  const selectCharacter = (character: Character) => {
    setSelectedCharacter(character);
    // 尝试从content中解析出结构化信息用于编辑
    const lines = character.content.split('\n');
    let gender = '无', personality = '', background = '';
    let currentSection = '';

    for (const line of lines) {
      if (line.startsWith('性别：')) {
        gender = line.replace('性别：', '').trim();
      } else if (line.startsWith('性格：')) {
        currentSection = 'personality';
      } else if (line.startsWith('背景：')) {
        currentSection = 'background';
      } else if (line.trim() && currentSection === 'personality') {
        personality += (personality ? '\n' : '') + line;
      } else if (line.trim() && currentSection === 'background') {
        background += (background ? '\n' : '') + line;
      }
    }

    setFormData({
      name: character.name,
      gender: (gender as CharacterGender) || '无',
      personality: personality,
      background: background
    });
  };

  // 删除角色
  const deleteCharacter = async (characterId: string) => {
    try {
      const { deleteCharacter: deleteCharacterFromDB } = await import('@/data');
      await deleteCharacterFromDB(characterId);

      const updatedCharacters = characters.filter(char => char.id !== characterId);
      setCharacters(updatedCharacters);

      if (selectedCharacter?.id === characterId) {
        setSelectedCharacter(null);
        setFormData({
          name: '',
          gender: '无',
          personality: '',
          background: ''
        });
      }
      // 通知数据变更
      onDataChange?.();
    } catch (error) {
      console.error('删除角色失败:', error);
    }
  };

  // 保存角色
  const saveCharacter = async () => {
    if (!selectedCharacter) return;

    // 将表单数据组合成文本内容
    const content = `性别：${formData.gender}\n\n性格：\n${formData.personality}\n\n背景：\n${formData.background}`;

    const updatedCharacter: Character = {
      ...selectedCharacter,
      name: formData.name.trim() || '未命名角色',
      content: content,
      updatedAt: new Date()
    };

    try {
      const { updateCharacter } = await import('@/data');
      await updateCharacter(updatedCharacter);

      const updatedCharacters = characters.map(char =>
        char.id === selectedCharacter.id ? updatedCharacter : char
      );
      setCharacters(updatedCharacters);
      setSelectedCharacter(updatedCharacter);
      // 通知数据变更
      onDataChange?.();
    } catch (error) {
      console.error('保存角色失败:', error);
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    if (selectedCharacter) {
      // 重新解析角色内容
      selectCharacter(selectedCharacter);
    }
  };

  // 处理表单输入
  const handleInputChange = (field: keyof CharacterFormData, value: string | CharacterGender) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] flex items-center justify-center mr-3 text-white shadow-md">
            <span className="material-icons text-lg">person</span>
          </div>
          <span style={{fontFamily: "'Ma Shan Zheng', cursive"}} className="text-xl text-text-dark">
            角色卡管理
          </span>
        </div>
      }
      footer={
        <div className="flex justify-end space-x-3 pt-2">
          <button
            onClick={() => {
              if (selectedCharacter) {
                saveCharacter();
              }
              onClose();
            }}
            className="px-4 py-2 bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] text-white rounded-lg hover:shadow-md transition-all"
          >
            确定
          </button>
          <button
            onClick={() => {
              cancelEdit();
              onClose();
            }}
            className="px-4 py-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition-all"
          >
            取消
          </button>
        </div>
      }
      maxWidth="max-w-5xl"
    >
      <div className="h-full flex">
        {/* 左侧：角色列表 */}
        <div className="w-1/3 border-r border-[rgba(156,111,224,0.2)] pr-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-text-dark font-medium text-base">角色列表</h3>
            <button
              onClick={createNewCharacter}
              className="px-3 py-1.5 bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] text-white text-sm rounded-lg flex items-center hover:shadow-md transition-all"
            >
              <span className="material-icons text-sm mr-1">add</span>
              新建
            </button>
          </div>

          <div className="space-y-2 max-h-[400px] overflow-y-auto">
            {characters.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <span className="material-icons text-4xl mb-2 block">person_add</span>
                <p>暂无角色</p>
                <p className="text-sm">点击"新建"创建第一个角色</p>
              </div>
            ) : (
              characters.map(character => (
                <div
                  key={character.id}
                  className={`p-3 rounded-lg cursor-pointer transition-all border ${
                    selectedCharacter?.id === character.id
                      ? 'bg-[rgba(156,111,224,0.15)] border-[#9c6fe0]'
                      : 'bg-white border-[rgba(156,111,224,0.1)] hover:bg-[rgba(156,111,224,0.05)]'
                  }`}
                  onClick={() => selectCharacter(character)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-text-dark truncate">
                        {character.name}
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        {(() => {
                          // 从content中提取性别信息
                          const lines = character.content.split('\n');
                          const genderLine = lines.find(line => line.startsWith('性别：'));
                          const gender = genderLine ? genderLine.replace('性别：', '').trim() : '';

                          return (
                            <>
                              {gender && gender !== '无' && (
                                <span className="inline-block bg-gray-100 px-2 py-0.5 rounded text-xs mr-2">
                                  {gender}
                                </span>
                              )}
                              {character.content && (
                                <span className="text-xs text-gray-400 truncate block">
                                  {character.content.substring(0, 30).replace(/\n/g, ' ')}
                                  {character.content.length > 30 && '...'}
                                </span>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteCharacter(character.id);
                      }}
                      className="text-gray-400 hover:text-red-500 ml-2"
                      title="删除角色"
                    >
                      <span className="material-icons text-sm">delete</span>
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 右侧：角色详情 */}
        <div className="flex-1 pl-4">
          {selectedCharacter ? (
            <div className="h-full flex flex-col">
              <div className="flex-1 space-y-4 overflow-y-auto">
                {/* 角色名称 */}
                <div>
                  <label className="block text-text-dark text-sm font-medium mb-2">
                    角色名称
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all"
                    placeholder="输入角色名称"
                  />
                </div>

                {/* 角色性别 */}
                <div>
                  <label className="block text-text-dark text-sm font-medium mb-2">
                    角色性别
                  </label>
                  <select
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value as CharacterGender)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all"
                  >
                    <option value="男">男</option>
                    <option value="女">女</option>
                    <option value="无">无</option>
                  </select>
                </div>

                {/* 角色性格 */}
                <div>
                  <label className="block text-text-dark text-sm font-medium mb-2">
                    角色性格
                  </label>
                  <textarea
                    value={formData.personality}
                    onChange={(e) => handleInputChange('personality', e.target.value)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all min-h-[100px] resize-none"
                    placeholder="描述角色的性格特点..."
                  />
                </div>

                {/* 背景信息 */}
                <div>
                  <label className="block text-text-dark text-sm font-medium mb-2">
                    背景信息
                  </label>
                  <textarea
                    value={formData.background}
                    onChange={(e) => handleInputChange('background', e.target.value)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all min-h-[120px] resize-none"
                    placeholder="描述角色的背景故事..."
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <div className="text-center">
                <span className="material-icons text-6xl mb-4 block">person_outline</span>
                <p>请选择一个角色查看详情</p>
                <p className="text-sm mt-2">或创建新的角色</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default CharacterCardModal;
