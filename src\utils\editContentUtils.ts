import { Chapter, Character, Outline, Setting, Knowledge } from '@/types';

export type EditMode = 'chapter' | 'character' | 'outline' | 'setting' | 'knowledge';

export interface EditContentResult {
  title: string;
  content: string;
  data: any;
}

export interface EditContentParams {
  editMode: EditMode;
  chapters: Chapter[];
  activeChapter: number;
  characters: Character[];
  activeCharacterId: string | null;
  outlines: Outline[];
  activeOutlineId: string | null;
  settings: Setting[];
  activeSettingId: string | null;
  knowledges: Knowledge[];
  activeKnowledgeId: number | null;
}

/**
 * 根据当前编辑模式获取对应的内容和标题
 */
export const getCurrentEditContent = (params: EditContentParams): EditContentResult => {
  const {
    editMode,
    chapters,
    activeChapter,
    characters,
    activeCharacterId,
    outlines,
    activeOutlineId,
    settings,
    activeSettingId,
    knowledges,
    activeKnowledgeId
  } = params;

  switch (editMode) {
    case 'outline':
      const selectedOutline = outlines.find(outline => outline.id === activeOutlineId);
      return {
        title: selectedOutline?.title || '',
        content: selectedOutline?.content || '',
        data: selectedOutline
      };
    case 'setting':
      const selectedSetting = settings.find(setting => setting.id === activeSettingId);
      return {
        title: selectedSetting?.title || '',
        content: selectedSetting?.content || '',
        data: selectedSetting
      };
    case 'character':
      const selectedCharacter = characters.find(char => char.id === activeCharacterId);
      // 直接返回角色的content字段
      return {
        title: selectedCharacter?.name || '',
        content: selectedCharacter?.content || '',
        data: selectedCharacter
      };
    case 'knowledge':
      const selectedKnowledge = knowledges.find(knowledge => knowledge.id === activeKnowledgeId);
      return {
        title: selectedKnowledge?.title || '',
        content: selectedKnowledge?.content || '',
        data: selectedKnowledge
      };
    default: // chapter
      return {
        title: chapters[activeChapter]?.title || '',
        content: chapters[activeChapter]?.content || '',
        data: chapters[activeChapter]
      };
  }
};
