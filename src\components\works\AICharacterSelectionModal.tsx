/**
 * AI创作助手角色卡选择模态窗口组件 - 简化版，仅用于选择角色
 */
import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';
import { Character } from '@/types/character';

interface AICharacterSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (character: Character) => void;
  workId?: number;
  initialSelectedIds?: string[];
}

/**
 * AI创作助手角色卡选择模态窗口组件 - 简化版，仅用于选择角色
 */
export const AICharacterSelectionModal: React.FC<AICharacterSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  workId,
  initialSelectedIds = []
}) => {
  // 状态
  const [characters, setCharacters] = useState<Character[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCharacters, setSelectedCharacters] = useState<Character[]>([]);

  // 加载角色数据
  const loadCharacters = () => {
    if (!workId) return;

    setIsLoading(true);
    setError('');

    try {
      const storageKey = `character_cards_${workId}`;
      const stored = localStorage.getItem(storageKey);
      
      if (stored) {
        const parsedCharacters = JSON.parse(stored).map((char: any) => ({
          ...char,
          createdAt: new Date(char.createdAt),
          updatedAt: new Date(char.updatedAt)
        }));
        
        // 按更新时间降序排序
        parsedCharacters.sort((a: Character, b: Character) => 
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
        
        setCharacters(parsedCharacters);

        // 设置初始选中的角色
        if (initialSelectedIds.length > 0) {
          const initialSelected = parsedCharacters.filter((char: Character) =>
            initialSelectedIds.includes(char.id)
          );
          setSelectedCharacters(initialSelected);
        }
      } else {
        setCharacters([]);
      }
    } catch (error) {
      console.error('加载角色数据失败:', error);
      setError('加载角色数据失败，请稍后再试。');
      setCharacters([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 当模态窗口打开时加载角色
  useEffect(() => {
    if (isOpen) {
      loadCharacters();
      // 关闭时重置状态
      return () => {
        setError('');
        setSearchTerm('');
      };
    }
  }, [isOpen, workId]);

  // 处理角色点击
  const handleCharacterClick = (character: Character) => {
    // 切换选中状态
    const characterId = character.id;
    const index = selectedCharacters.findIndex(char => char.id === characterId);

    if (index === -1) {
      // 添加到选中列表
      setSelectedCharacters([...selectedCharacters, character]);
      onSelect(character); // 通知父组件
    } else {
      // 从选中列表移除
      const newSelectedCharacters = [...selectedCharacters];
      newSelectedCharacters.splice(index, 1);
      setSelectedCharacters(newSelectedCharacters);
      onSelect(character); // 通知父组件（父组件会处理移除逻辑）
    }
  };

  // 过滤角色
  const filteredCharacters = characters.filter(character =>
    character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    character.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] flex items-center justify-center mr-3 text-white shadow-md">
            <span className="material-icons text-lg">person</span>
          </div>
          <span style={{fontFamily: "'Ma Shan Zheng', cursive"}} className="text-xl text-text-dark">
            选择角色
          </span>
        </div>
      }
      footer={
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="ghibli-button outline text-sm py-2 transition-all duration-200"
          >
            完成
          </button>
        </div>
      }
      maxWidth="max-w-2xl"
    >
      <div className="h-[500px] flex flex-col">
        {/* 搜索框 */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              placeholder="搜索角色名称、性格或背景..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all"
            />
            <span className="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              search
            </span>
          </div>
        </div>

        {/* 角色列表 */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#9c6fe0] mx-auto mb-2"></div>
                <p className="text-gray-500">加载中...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-red-500">
                <span className="material-icons text-4xl mb-2 block">error_outline</span>
                <p>{error}</p>
              </div>
            </div>
          ) : filteredCharacters.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <span className="material-icons text-4xl mb-2 block">person_add</span>
                <p>暂无角色</p>
                <p className="text-sm">请先在角色卡管理中创建角色</p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredCharacters.map(character => {
                const isSelected = selectedCharacters.some(char => char.id === character.id);
                return (
                  <div
                    key={character.id}
                    onClick={() => handleCharacterClick(character)}
                    className={`p-4 rounded-lg cursor-pointer transition-all border ${
                      isSelected
                        ? 'bg-[rgba(156,111,224,0.15)] border-[#9c6fe0]'
                        : 'bg-white border-[rgba(156,111,224,0.1)] hover:bg-[rgba(156,111,224,0.05)]'
                    }`}
                  >
                    <div className="flex items-start">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center mb-2">
                          <h3 className="font-medium text-text-dark truncate mr-2">
                            {character.name}
                          </h3>
                          {(() => {
                            // 从content中提取性别信息
                            const lines = character.content.split('\n');
                            const genderLine = lines.find(line => line.startsWith('性别：'));
                            const gender = genderLine ? genderLine.replace('性别：', '').trim() : '';

                            return gender && gender !== '无' && (
                              <span className="inline-block bg-gray-100 px-2 py-0.5 rounded text-xs text-gray-600">
                                {gender}
                              </span>
                            );
                          })()}
                        </div>
                        {character.content && (
                          <div className="text-sm text-gray-600">
                            <span className="line-clamp-3">{character.content.replace(/\n/g, ' ')}</span>
                          </div>
                        )}
                      </div>
                      <div className="ml-3 flex-shrink-0">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                          isSelected
                            ? 'bg-[#9c6fe0] border-[#9c6fe0]'
                            : 'border-gray-300'
                        }`}>
                          {isSelected && (
                            <span className="material-icons text-white text-sm">check</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};
