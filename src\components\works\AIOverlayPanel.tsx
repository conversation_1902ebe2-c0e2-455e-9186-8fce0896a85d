/**
 * AI覆盖层面板组件 - 从下方弹出覆盖编辑区
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getKnowledgeByWorkId, getWorkById, updateWork, Work } from '@/data';
import { Knowledge } from '@/data';
import { workContentUtils } from '@/lib/utils';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  usage_count?: number;
}
import { generateAIContentStream, MODELS, Message, Usage } from '@/lib/AIserver';
import { BillingService } from '@/lib/billing';
import { AIKnowledgeSelectionModal } from '@/components/knowledgebase/AIKnowledgeSelectionModal';
import { AICharacterSelectionModal } from '@/components/works/AICharacterSelectionModal';
import { ChapterAssociationModal } from '@/components/works/ChapterAssociationModal';
import { PromptSelectionModal } from '@/components/prompts/PromptSelectionModal';
import { Character } from '@/types/character';
import { Chapter } from '@/types/chapter';
// 本地提示词store已删除
import { getCurrentUser } from '@/services/userService';
import StarLogo from '@/components/common/StarLogo/StarLogo';
import { MessageList } from '@/components/chat/MessageList';
import { ChatInput } from '@/components/chat/ChatInput';

// 聊天消息接口
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

// 提示词类型标签映射
const PROMPT_TYPE_LABELS_MAP = {
  'ai_writing': 'AI写作',
  'ai_polishing': 'AI润色'
};

// 组件属性
interface AIOverlayPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertToEditor: (content: string) => void;
  currentContent: string;
  chapters?: Chapter[];
  activeChapter?: number;
  initialPromptType?: 'ai_writing' | 'ai_polishing';
  workId?: number;
  defaultIsDescending?: boolean;
  onDataChange?: () => void; // 通知父组件数据已更新
  editMode?: 'chapter' | 'character' | 'outline' | 'setting' | 'knowledge';
  onContentChange?: (content: string) => void; // 处理非章节内容的编辑
  onChapterChange?: (chapterIndex: number, content: string) => void; // 处理章节内容的直接同步
}

/**
 * AI覆盖层面板组件
 */
export const AIOverlayPanel: React.FC<AIOverlayPanelProps> = ({
  isOpen,
  onClose,
  onInsertToEditor,
  currentContent,
  chapters: initialChapters = [],
  activeChapter: initialActiveChapter = 0,
  initialPromptType = 'ai_writing',
  workId,
  defaultIsDescending = false,
  onDataChange,
  editMode = 'chapter',
  onContentChange,
  onChapterChange
}) => {
  // 收起状态管理
  const [isMinimized, setIsMinimized] = useState(false);
  // 左侧面板视图切换状态
  const [leftPanelView, setLeftPanelView] = useState<'settings' | 'editor'>('settings');
  // 基本状态 - 只保留用户提示词
  const [userPrompts, setUserPrompts] = useState<UserPrompt[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [elapsedTime, setElapsedTime] = useState(0); // 计时器状态
  // 确认删除窗口状态
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 对话相关状态
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(null);

  // 内部章节数据管理
  const [chapters, setChapters] = useState<Chapter[]>(initialChapters);
  const [activeChapter, setActiveChapter] = useState(initialActiveChapter);
  const [work, setWork] = useState<Work | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);

  // 上次保存的内容引用（复制自现有编辑器）
  const lastSavedContent = useRef<string>('');
  // 保存计时器（复制自现有编辑器）
  const saveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isGenerating) {
      // 重置计时器
      setElapsedTime(0);
      // 启动计时器，每100ms更新一次
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 0.1);
      }, 100);
    } else {
      // 停止计时器
      if (interval) {
        clearInterval(interval);
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isGenerating]);

  // 功能选择状态
  const [currentPromptType, setCurrentPromptType] = useState<'ai_writing' | 'ai_polishing'>('ai_writing');
  const [selectedModel, setSelectedModel] = useState(MODELS.LLM_TEST);
  const [selectedPrompt, setSelectedPrompt] = useState<UserPrompt | null>(null);
  const [isCustomPromptMode, setIsCustomPromptMode] = useState(false);
  const [customPromptContent, setCustomPromptContent] = useState('');
  const [userInput, setUserInput] = useState('');

  // 知识库和角色相关状态
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge[]>([]);
  const [selectedCharacters, setSelectedCharacters] = useState<Character[]>([]);
  
  // 模态窗口状态
  const [showKnowledgeModal, setShowKnowledgeModal] = useState(false);
  const [showCharacterModal, setShowCharacterModal] = useState(false);
  const [showChapterModal, setShowChapterModal] = useState(false);
  const [showPromptSelectionModal, setShowPromptSelectionModal] = useState(false);
  
  // 选择状态
  const [selectedKnowledgeIds, setSelectedKnowledgeIds] = useState<number[]>([]);
  const [selectedCharacterIds, setSelectedCharacterIds] = useState<string[]>([]);
  const [selectedChapters, setSelectedChapters] = useState<number[]>([]);
  const [isAutoAssociate, setIsAutoAssociate] = useState(false);
  const [autoAssociateCount, setAutoAssociateCount] = useState(3);
  const [isDescending, setIsDescending] = useState(defaultIsDescending);
  
  // 引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // 本地提示词store已删除

  // 加载作品数据
  const loadWorkData = async () => {
    if (!workId) return;

    try {
      const workData = await getWorkById(workId);
      if (workData) {
        setWork(workData);
        const parsedChapters = workContentUtils.parseContent(workData.content);
        setChapters(parsedChapters);
        setLastSavedAt(new Date(workData.updatedAt));

        // 初始化上次保存的内容（复制自现有编辑器）
        lastSavedContent.current = workData.content;

        // 设置激活章节
        if (parsedChapters.length > 0) {
          const validActiveChapter = Math.min(initialActiveChapter, parsedChapters.length - 1);
          setActiveChapter(Math.max(0, validActiveChapter));
        }
      }
    } catch (error) {
      console.error('加载作品数据失败:', error);
    }
  };

  // 触发保存的函数（复制自现有编辑器）
  const triggerSave = useCallback(async () => {
    if (!work) return;

    // 获取当前内容的序列化字符串
    const currentContent = workContentUtils.stringifyChapters(chapters);

    // 只有当内容有变化时才保存
    if (currentContent !== lastSavedContent.current) {
      setIsSaving(true);

      try {
        const updatedWork = {
          ...work,
          content: currentContent,
          updatedAt: new Date()
        };

        await updateWork(updatedWork);
        setWork(updatedWork);
        setLastSavedAt(new Date());
        // 更新上次保存的内容
        lastSavedContent.current = currentContent;

        // 章节内容已通过onChapterChange直接同步，无需调用onDataChange
        console.log('保存成功', new Date().toLocaleTimeString());
      } catch (error) {
        console.error('保存失败:', error);
      } finally {
        setIsSaving(false);
      }
    }
  }, [work, chapters, onDataChange]);

  // 安排延迟保存（复制自现有编辑器）
  const scheduleSave = useCallback(() => {
    // 取消已有的定时器
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
    }

    // 设置新的定时器，1秒后保存
    saveTimerRef.current = setTimeout(() => {
      triggerSave();
      saveTimerRef.current = null;
    }, 1000);
  }, [triggerSave]);

  // 处理章节内容变更（复制自现有编辑器）
  const handleChange = (content: string) => {
    if (activeChapter >= 0 && activeChapter < chapters.length) {
      const newChapters = [...chapters];
      newChapters[activeChapter] = {
        ...newChapters[activeChapter],
        content: content
      };
      setChapters(newChapters);

      // 直接同步到主编辑器（新增）
      if (onChapterChange) {
        onChapterChange(activeChapter, content);
      }

      // 安排延迟保存
      scheduleSave();
    }
  };

  // 初始化
  useEffect(() => {
    if (isOpen) {
      loadPromptsData();
      loadWorkData();
    }
  }, [isOpen, currentPromptType, workId]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimerRef.current) {
        clearTimeout(saveTimerRef.current);
      }
    };
  }, []);

  // 监听外部传入的章节和激活章节变化，确保同步
  useEffect(() => {
    setChapters(initialChapters);
    setActiveChapter(initialActiveChapter);
  }, [initialChapters, initialActiveChapter]);

  // 加载提示词数据 - 根据当前分类加载提示词
  const loadPromptsData = async () => {
    try {
      // 加载用户提示词（根据当前分类）
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: currentPromptType,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserPrompts(data.data || []);

          // 默认直接选择第一个提示词
          const allPrompts = data.data || [];
          if (allPrompts.length > 0) {
            setSelectedPrompt(allPrompts[0]);
          }
        }
      }
    } catch (error) {
      console.error('加载提示词失败:', error);
    }
  };



  // 处理知识库选择
  const handleKnowledgeSelect = async (knowledge: Knowledge) => {
    const newSelectedIds = [...selectedKnowledgeIds, knowledge.id!];
    setSelectedKnowledgeIds(newSelectedIds);
    setSelectedKnowledge([...selectedKnowledge, knowledge]);
    setShowKnowledgeModal(false);
  };

  // 处理角色选择
  const handleCharacterSelect = (character: Character) => {
    const newSelectedIds = [...selectedCharacterIds, character.id];
    setSelectedCharacterIds(newSelectedIds);
    setSelectedCharacters([...selectedCharacters, character]);
    setShowCharacterModal(false);
  };



  // 处理消息编辑
  const handleEditMessage = (messageId: string, newContent: string) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, content: newContent }
        : msg
    ));
  };

  // 处理消息重新生成
  const handleRegenerateMessage = async (messageId: string) => {
    if (isGenerating) return;

    // 找到要重新生成的消息
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    // 获取该消息之前的所有消息作为上下文
    const contextMessages = messages.slice(0, messageIndex);

    // 找到触发这个AI回复的用户消息
    const lastUserMessage = contextMessages.filter(msg => msg.role === 'user').pop();
    if (!lastUserMessage) return;

    // 重置要重新生成的消息
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, content: '', isStreaming: true }
        : msg
    ));

    setCurrentStreamingId(messageId);
    setIsGenerating(true);
    setError('');

    try {
      // 构建消息历史（不包括要重新生成的消息及其之后的消息）
      const conversationMessages: Message[] = [
        // 系统提示词
        {
          role: 'system',
          content: getSystemPrompt()
        },
        // 历史对话（不包括要重新生成的消息）
        ...contextMessages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        }))
      ];

      // 流式生成
      await generateAIContentStream(
        conversationMessages,
        { model: selectedModel },
        (chunk) => {
          if (!chunk) return;

          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? { ...msg, content: msg.content + chunk }
              : msg
          ));
        },
        (usage) => {
          console.log('AI重新生成完成，使用情况:', usage);
        }
      );

      // 完成流式生成，更新消息状态
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isStreaming: false }
          : msg
      ));

    } catch (error: any) {
      console.error('AI重新生成失败:', error);

      // 在消息中显示错误
      const errorMessage = error.name === 'AbortError' ? '[重新生成已停止]' : `重新生成错误：${error.message || '500'}`;
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isStreaming: false, content: errorMessage }
          : msg
      ));
    } finally {
      setIsGenerating(false);
      setCurrentStreamingId(null);
    }
  };

  // 处理发送消息
  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isGenerating) return;

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    
    // 创建AI消息占位符
    const aiMessageId = (Date.now() + 1).toString();
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true
    };
    
    setMessages(prev => [...prev, aiMessage]);
    setCurrentStreamingId(aiMessageId);
    setIsGenerating(true);
    setError('');

    try {
      // 构建消息历史
      const conversationMessages: Message[] = [
        // 系统提示词
        {
          role: 'system',
          content: getSystemPrompt()
        },
        // 历史对话
        ...messages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        })),
        // 当前用户消息
        {
          role: 'user',
          content: message
        }
      ];

      // 流式生成
      await generateAIContentStream(
        conversationMessages,
        { model: selectedModel },
        (chunk) => {
          if (!chunk) return;

          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: msg.content + chunk }
              : msg
          ));
        },
        (usage) => {
          console.log('AI生成完成，使用情况:', usage);
        }
      );

      // 完成流式生成，更新消息状态
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessageId
          ? { ...msg, isStreaming: false }
          : msg
      ));
    } catch (error: any) {
      console.error('发送消息失败:', error);
      // 在AI消息气泡中显示错误代码
      const errorCode = error.message || '500';
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessageId
          ? { ...msg, isStreaming: false, content: `错误：${errorCode}` }
          : msg
      ));
    } finally {
      setIsGenerating(false);
      setCurrentStreamingId(null);
    }
  };

  // 获取系统提示词
  const getSystemPrompt = (): string => {
    let systemPrompt = '';

    // 基础提示词
    if (isCustomPromptMode && customPromptContent.trim()) {
      systemPrompt = customPromptContent;
    } else if (selectedPrompt) {
      // 用户提示词使用ID标识，由后端处理
      systemPrompt = `__USER_PROMPT_ID__:${selectedPrompt.id}`;
    } else {
      // 默认提示词
      systemPrompt = '你是一个专业的AI助手，帮助用户处理各种文本任务。';
    }

    // 添加用户输入
    if (userInput.trim()) {
      systemPrompt += `\n\n用户补充要求：${userInput}`;
    }

    // 添加知识库信息
    if (selectedKnowledge.length > 0) {
      systemPrompt += '\n\n相关知识库信息：\n';
      selectedKnowledge.forEach((knowledge, index) => {
        systemPrompt += `${index + 1}. ${knowledge.title}：${knowledge.content}\n`;
      });
    }

    // 添加角色信息
    if (selectedCharacters.length > 0) {
      systemPrompt += '\n\n相关角色信息：\n';
      selectedCharacters.forEach((character, index) => {
        systemPrompt += `${index + 1}. ${character.name}`;
        if (character.content) {
          systemPrompt += `：${character.content}`;
        }
        systemPrompt += '\n';
      });
    }

    // 添加章节信息 - 完整发送，不截断
    if (selectedChapters.length > 0 && chapters.length > 0) {
      systemPrompt += '\n\n相关章节内容：\n';
      selectedChapters.forEach((chapterIndex, index) => {
        const chapter = chapters[chapterIndex];
        if (chapter) {
          systemPrompt += `${index + 1}. ${chapter.title}：${chapter.content}\n`;
        }
      });
    }

    return systemPrompt;
  };

  // 滚动到底部
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // 处理ESC键关闭和body滚动
  useEffect(() => {
    if (isOpen) {
      // 禁止body滚动
      document.body.style.overflow = 'hidden';

      // ESC键关闭
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }
      };

      document.addEventListener('keydown', handleEscape);

      return () => {
        document.body.style.overflow = '';
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // 处理收起功能 - 不清除上下文
  const handleMinimize = () => {
    setIsMinimized(true);
  };

  // 处理展开功能
  const handleExpand = () => {
    setIsMinimized(false);
  };

  // 处理显示清除确认窗口
  const handleShowClearConfirm = () => {
    setShowClearConfirm(true);
  };

  // 处理清除上下文功能 - 只清除对话历史，不关闭面板
  const handleClearContext = () => {
    // 只清除对话历史
    setMessages([]);
    setCurrentStreamingId(null);
    setIsGenerating(false);
    setError('');
    setElapsedTime(0);
    // 关闭确认窗口
    setShowClearConfirm(false);
  };

  // 处理关闭功能 - 清除对话历史并关闭面板
  const handleClose = () => {
    // 清除对话历史
    handleClearContext();

    // 重置收起状态
    setIsMinimized(false);

    // 调用父组件的关闭回调
    onClose();
  };

  // 如果面板未打开，不渲染任何内容
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* 覆盖层面板 - 相对于编辑器容器定位 */}
      <div className={`absolute bottom-0 left-0 right-0 bg-white shadow-2xl z-[9999] flex flex-col overflow-hidden ${
        isMinimized
          ? 'h-16 rounded-t-xl ai-overlay-minimized'
          : 'top-0 rounded-t-2xl ai-overlay-slide-up'
      }`}>

        {/* 收起状态下的小按钮 */}
        {isMinimized ? (
          <div className="flex-1 flex items-center justify-center bg-card-color cursor-pointer hover:bg-[rgba(120,180,140,0.1)] transition-all duration-200 border-t border-[rgba(120,180,140,0.2)]" onClick={handleExpand}>
            <div className="flex items-center space-x-2 text-text-dark">
              <span className="material-icons text-lg text-primary-green">expand_less</span>
              <span className="text-base font-medium">AI创作助手</span>
              <span className="material-icons text-lg text-primary-green">psychology</span>
            </div>
          </div>
        ) : (
          <>
            {/* 顶部按钮组 - 右上角 */}
            <div className="absolute top-4 right-4 flex space-x-2 z-[10000]">
              {/* 清除按钮 */}
              <button
                onClick={handleShowClearConfirm}
                className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-all duration-200 shadow-sm hover:scale-105"
                title="清除上下文"
              >
                <span className="material-icons text-lg">delete</span>
              </button>

              {/* 收起按钮 */}
              <button
                onClick={handleMinimize}
                className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-all duration-200 shadow-sm hover:scale-105"
                title="收起"
              >
                <span className="material-icons text-lg">expand_more</span>
              </button>

              {/* 关闭按钮 */}
              <button
                onClick={handleClose}
                className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-all duration-200 shadow-sm hover:scale-105"
                title="关闭并清除对话历史"
              >
                <span className="material-icons text-lg">close</span>
              </button>
            </div>

            {/* 左右两栏布局 - 只在展开状态显示 */}
            <div className="flex-1 flex overflow-hidden bg-gradient-to-b from-gray-50 to-white">
              {/* 左侧栏：可切换的设置区和编辑器 */}
              <div className="w-1/2 border-r border-gray-200 flex flex-col">
                {/* 顶部切换按钮 */}
                <div className="flex border-b border-gray-200 bg-white">
                  <button
                    onClick={() => setLeftPanelView('settings')}
                    className={`flex-1 px-4 py-3 text-sm font-medium transition-all duration-200 ${
                      leftPanelView === 'settings'
                        ? 'bg-[#00C250] text-white border-b-2 border-[#00C250]'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                    }`}
                  >
                    <span className="material-icons mr-2 text-lg">settings</span>
                    设置
                  </button>
                  <button
                    onClick={() => setLeftPanelView('editor')}
                    className={`flex-1 px-4 py-3 text-sm font-medium transition-all duration-200 ${
                      leftPanelView === 'editor'
                        ? 'bg-[#00C250] text-white border-b-2 border-[#00C250]'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                    }`}
                  >
                    <span className="material-icons mr-2 text-lg">edit</span>
                    编辑器
                  </button>
                </div>

                {/* 内容区域 - 根据状态切换显示 */}
                {leftPanelView === 'settings' ? (
                  /* 设置区域 - 全高显示 */
                  <div className="flex-1 overflow-y-auto px-4 py-4">

                  {/* 配置选项区域 */}
                  <div className="space-y-4">

                  {/* 模型选择 */}
                  <div className="bg-[#f7f2ea] p-3 rounded-lg border border-[rgba(120,180,140,0.15)]">
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-[#5a9d6b] mr-2 text-lg">model_training</span>
                      <h3 className="text-base font-medium text-text-dark" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>选择模型</h3>
                    </div>
                    <select
                      value={selectedModel}
                      onChange={(e) => setSelectedModel(e.target.value)}
                      className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green text-base"
                    >
                      <option value={MODELS.LLM_TEST}>测试版（免费）</option>
                      <option value={MODELS.LLM_CLAUDE}>克劳德（5X消耗）</option>
                    </select>
                  </div>

                  {/* 提示词选择 */}
                  <div className="bg-[#f7f2ea] p-3 rounded-lg border border-[rgba(120,180,140,0.15)]">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <span className="material-icons text-[#9C6FE0] mr-2 text-lg">lightbulb</span>
                        <h3 className="text-base font-medium text-text-dark" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>
                          {isCustomPromptMode ? '自定义提示词' : '选择提示词'}
                        </h3>
                      </div>
                      <div className="flex space-x-1">
                        <button
                          onClick={() => setIsCustomPromptMode(!isCustomPromptMode)}
                          className="px-2 py-1 bg-[rgba(156,111,224,0.1)] text-[#9C6FE0] rounded text-base hover:bg-[rgba(156,111,224,0.2)] transition-all"
                        >
                          {isCustomPromptMode ? '返回选择' : '自定义'}
                        </button>
                        <button
                          onClick={() => setShowPromptSelectionModal(true)}
                          className="px-2 py-1 bg-[rgba(109,92,77,0.1)] text-[#6d5c4d] rounded text-base hover:bg-[rgba(109,92,77,0.2)] transition-all"
                        >
                          仓库
                        </button>
                      </div>
                    </div>
                    {!isCustomPromptMode ? (
                      <select
                        value={selectedPrompt?.id || ''}
                        onChange={(e) => {
                          const promptId = e.target.value;
                          const allPrompts = userPrompts;
                          const prompt = allPrompts.find(p => String(p.id) === promptId) || null;
                          setSelectedPrompt(prompt);
                        }}
                        className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green text-base"
                      >
                        <option value="" disabled>{PROMPT_TYPE_LABELS_MAP[currentPromptType as keyof typeof PROMPT_TYPE_LABELS_MAP]}</option>
                        {userPrompts.map(prompt => (
                          <option key={prompt.id} value={String(prompt.id)}>
                            📋 {prompt.title}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <textarea
                        value={customPromptContent}
                        onChange={(e) => setCustomPromptContent(e.target.value)}
                        className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green text-base min-h-[60px] resize-none"
                        placeholder="请输入您的自定义提示词..."
                      />
                    )}
                  </div>

                  {/* 用户输入 */}
                  <div className="bg-[#f7f2ea] p-3 rounded-lg border border-[rgba(120,180,140,0.15)]">
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-[#E0C56F] mr-2 text-lg">chat</span>
                      <h3 className="text-base font-medium text-text-dark" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>用户输入</h3>
                    </div>
                    <textarea
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green text-base min-h-[60px] resize-none"
                      placeholder="在此输入您的具体要求..."
                    />
                  </div>
                </div>

                    {/* 关联选项 */}
                    <div className="grid grid-cols-1 gap-3">
                    {/* 知识库关联 */}
                    {workId !== undefined && (
                      <div className="bg-[#f7f2ea] p-3 rounded-lg border border-[rgba(120,180,140,0.15)]">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <span className="material-icons text-[#7D85CC] mr-2 text-lg">folder_special</span>
                            <h3 className="text-base font-medium text-text-dark" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>关联知识</h3>
                          </div>
                          <button
                            onClick={() => setShowKnowledgeModal(true)}
                            className="px-2 py-1 bg-gradient-to-br from-[#7D85CC] to-[#6b73b3] text-white text-base rounded hover:shadow-md transition-all"
                          >
                            选择知识
                          </button>
                        </div>
                        <div className="text-base text-text-medium">
                          {selectedKnowledge.length > 0 ? `已选择 ${selectedKnowledge.length} 个知识条目` : '暂无选择'}
                        </div>
                      </div>
                    )}

                    {/* 角色卡关联 */}
                    {workId !== undefined && (
                      <div className="bg-[#f7f2ea] p-3 rounded-lg border border-[rgba(120,180,140,0.15)]">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <span className="material-icons text-[#9c6fe0] mr-2 text-lg">person</span>
                            <h3 className="text-base font-medium text-text-dark" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>角色卡片</h3>
                          </div>
                          <button
                            onClick={() => setShowCharacterModal(true)}
                            className="px-2 py-1 bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] text-white text-base rounded hover:shadow-md transition-all"
                          >
                            选择角色
                          </button>
                        </div>
                        <div className="text-base text-text-medium">
                          {selectedCharacters.length > 0 ? `已选择 ${selectedCharacters.length} 个角色` : '暂无选择'}
                        </div>
                      </div>
                    )}

                    {/* 章节关联 */}
                    {chapters.length > 0 && (
                      <div className="bg-[#f7f2ea] p-3 rounded-lg border border-[rgba(120,180,140,0.15)]">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <span className="material-icons text-[#E0976F] mr-2 text-lg">menu_book</span>
                            <h3 className="text-base font-medium text-text-dark" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>章节关联</h3>
                          </div>
                          <button
                            onClick={() => setShowChapterModal(true)}
                            className="px-2 py-1 bg-gradient-to-br from-[#E0976F] to-[#e08a58] text-white text-base rounded hover:shadow-md transition-all"
                          >
                            选择章节
                          </button>
                        </div>
                        <div className="text-base text-text-medium">
                          {selectedChapters.length > 0 ? `已选择 ${selectedChapters.length} 个章节` : '暂无选择'}
                        </div>
                      </div>
                    )}
                  </div>


                  </div>
                ) : (
                  /* 编辑器区域 - 全高显示 */
                  <div className="flex-1 p-4 overflow-hidden">
                  {/* 美化的状态栏 */}
                  <div className="relative mb-4">
                    {/* 装饰性背景 */}
                    <div className="absolute inset-0 bg-gradient-to-r from-[#f8fffe] via-[#f0fdf4] to-[#f8fffe] rounded-xl opacity-60"></div>
                    <div className="absolute top-0 right-0 w-20 h-8 bg-gradient-to-l from-[#00C250]/10 to-transparent rounded-bl-xl"></div>

                    <div className="relative flex justify-between items-center p-3 bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.2)] rounded-xl shadow-sm">
                      {/* 左侧：编辑信息 */}
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-br from-[#00C250] to-[#00A844] rounded-full flex items-center justify-center mr-3 shadow-md">
                          <span className="material-icons text-white text-sm">edit</span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-800">
                            {chapters[activeChapter] ? chapters[activeChapter].title : '选择章节进行编辑'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {editMode === 'chapter' ? '章节编辑' :
                             editMode === 'outline' ? '大纲编辑' :
                             editMode === 'setting' ? '设定编辑' :
                             editMode === 'character' ? '角色编辑' : '知识编辑'}
                          </p>
                        </div>
                      </div>

                      {/* 右侧：保存状态 */}
                      <div className="flex items-center space-x-3">
                        {isSaving && (
                          <div className="flex items-center px-3 py-1 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-full">
                            <div className="w-4 h-4 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-2">
                              <span className="material-icons animate-spin text-white text-xs">sync</span>
                            </div>
                            <span className="text-blue-700 text-xs font-medium">保存中...</span>
                          </div>
                        )}
                        {lastSavedAt && !isSaving && (
                          <div className="flex items-center px-3 py-1 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-full">
                            <div className="w-4 h-4 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mr-2">
                              <span className="material-icons text-white text-xs">check_circle</span>
                            </div>
                            <span className="text-green-700 text-xs font-medium">
                              已保存 {lastSavedAt.toLocaleTimeString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 美化的编辑器容器 */}
                  <div className="relative h-[calc(100%-80px)]">
                    {/* 装饰性背景和边框 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white via-[#fefffe] to-[#f9fffe] rounded-2xl"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-[rgba(120,180,140,0.02)] to-[rgba(90,157,107,0.04)] rounded-2xl"></div>

                    {/* 装饰性边角 */}
                    <div className="absolute top-0 left-0 w-6 h-6 bg-gradient-to-br from-[#00C250]/20 to-transparent rounded-tl-2xl"></div>
                    <div className="absolute bottom-0 right-0 w-8 h-8 bg-gradient-to-tl from-[#00C250]/15 to-transparent rounded-br-2xl"></div>

                    {/* 纸张纹理效果 */}
                    <div className="absolute inset-0 opacity-30 rounded-2xl" style={{
                      backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(120,180,140,0.06)' fill-opacity='1'%3E%3Ccircle cx='3' cy='3' r='0.5'/%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>

                    <textarea
                      value={editMode === 'chapter' ? (chapters && chapters[activeChapter] ? chapters[activeChapter].content : '') : currentContent}
                      onChange={(e) => {
                        // 根据编辑模式处理内容更新，包括删除操作
                        const newValue = e.target.value;
                        if (editMode === 'chapter') {
                          handleChange(newValue);
                        } else if (onContentChange) {
                          onContentChange(newValue);
                        }
                      }}
                      onInput={(e) => {
                        // 处理所有输入事件，包括删除操作
                        const newValue = (e.target as HTMLTextAreaElement).value;
                        if (editMode === 'chapter') {
                          handleChange(newValue);
                        } else if (onContentChange) {
                          onContentChange(newValue);
                        }
                      }}
                      onKeyDown={(e) => {
                        // 处理删除键和退格键
                        if (e.key === 'Delete' || e.key === 'Backspace') {
                          // 延迟处理，确保DOM更新后获取最新值
                          setTimeout(() => {
                            const newValue = (e.target as HTMLTextAreaElement).value;
                            if (editMode === 'chapter') {
                              handleChange(newValue);
                            } else if (onContentChange) {
                              onContentChange(newValue);
                            }
                          }, 0);
                        }
                      }}
                      onCompositionEnd={(e) => {
                        // 输入法组合结束后，再次更新内容以确保获取最终确认的文本（复制自现有编辑器）
                        console.log('输入法组合结束，确保内容更新');
                        const newValue = (e.target as HTMLTextAreaElement).value;
                        if (editMode === 'chapter') {
                          handleChange(newValue);
                        } else if (onContentChange) {
                          onContentChange(newValue);
                        }
                      }}
                      className="relative w-full h-full resize-none bg-transparent border-2 border-[rgba(120,180,140,0.2)] rounded-2xl p-6 focus:outline-none focus:border-[#00C250] focus:shadow-lg focus:shadow-[rgba(0,194,80,0.1)] transition-all duration-300 hover:border-[rgba(120,180,140,0.3)] hover:shadow-md"
                      style={{
                        fontFamily: "'思源黑体', 'Noto Sans SC', sans-serif",
                        fontSize: '16px',
                        fontWeight: 400,
                        lineHeight: '2.0',
                        color: 'var(--text-dark)',
                        overflow: 'auto',
                        zIndex: 10
                      }}
                      placeholder={editMode === 'chapter' ? "开始创作你的故事..." :
                                  editMode === 'outline' ? "请输入大纲内容..." :
                                  editMode === 'setting' ? "请输入设定内容..." :
                                  editMode === 'character' ? "请输入角色描述..." :
                                  "请输入知识内容..."}
                    />

                    {/* 底部装饰线 */}
                    <div className="absolute bottom-2 left-6 right-6 h-px bg-gradient-to-r from-transparent via-[rgba(120,180,140,0.2)] to-transparent"></div>
                  </div>
                </div>
                )}
              </div>

              {/* 右侧栏：对话流 */}
              <div className="w-1/2 flex flex-col">
                {/* 对话消息列表 */}
                <div className="flex-1 overflow-y-auto px-4 py-4">

                  <div className="mb-3 pb-3 border-b border-gray-200">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <span className="material-icons mr-2 text-lg">chat</span>
                      对话记录
                    </h4>
                  </div>
                  <MessageList
                    messages={messages}
                    onEditMessage={handleEditMessage}
                    onRegenerateMessage={handleRegenerateMessage}
                  />
                  <div ref={messagesEndRef} />
          </div>

                {/* 底部输入区域 */}
                <div className="border-t border-gray-200 bg-white">
                  {/* 输入框 */}
                  <ChatInput
                    onSendMessage={handleSendMessage}
                    disabled={isGenerating}
                    placeholder={isGenerating ? 'AI正在思考中...' : '输入消息开始对话...'}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* 各种模态窗口 */}
      {showKnowledgeModal && (
        <AIKnowledgeSelectionModal
          isOpen={showKnowledgeModal}
          onClose={() => setShowKnowledgeModal(false)}
          onSelect={handleKnowledgeSelect}
          workId={workId}
          initialSelectedIds={selectedKnowledgeIds}
        />
      )}

      {showCharacterModal && (
        <AICharacterSelectionModal
          isOpen={showCharacterModal}
          onClose={() => setShowCharacterModal(false)}
          onSelect={handleCharacterSelect}
          workId={workId}
          initialSelectedIds={selectedCharacterIds}
        />
      )}

      {showChapterModal && (
        <ChapterAssociationModal
          isOpen={showChapterModal}
          onClose={() => setShowChapterModal(false)}
          onConfirm={(chapters, isAuto, count) => {
            setSelectedChapters(chapters);
            setIsAutoAssociate(isAuto);
            setAutoAssociateCount(count);
            setShowChapterModal(false);
          }}
          chapters={chapters}
          initialSelectedChapters={selectedChapters}
          isDescending={isDescending}
          initialIsAutoAssociate={isAutoAssociate}
          initialAutoAssociateCount={autoAssociateCount}
        />
      )}

      {showPromptSelectionModal && (
        <PromptSelectionModal
          isOpen={showPromptSelectionModal}
          onClose={() => setShowPromptSelectionModal(false)}
          onSelect={(prompt: UserPrompt) => {
            setSelectedPrompt(prompt);
            // 根据选择的提示词自动更新分类
            if (prompt.type && prompt.type !== currentPromptType && ['ai_writing', 'ai_polishing'].includes(prompt.type)) {
              setCurrentPromptType(prompt.type as 'ai_writing' | 'ai_polishing');
            }
            setShowPromptSelectionModal(false);
          }}
          promptType={currentPromptType}
          initialSelectedId={selectedPrompt?.id}
        />
      )}

      {/* 确认清除上下文模态窗口 */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10001]">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <span className="material-icons text-red-600">warning</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">确认清除上下文</h3>
            </div>

            <p className="text-gray-600 mb-6">
              您确定要清除所有对话历史吗？此操作无法撤销。
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowClearConfirm(false)}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
              >
                取消
              </button>
              <button
                onClick={handleClearContext}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
              >
                确认清除
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
