/**
 * MinIO客户端配置
 */
import * as Minio from 'minio';

// MinIO配置
const minioConfig = {
  endPoint: process.env.MINIO_ENDPOINT || '114.55.8.214',
  port: parseInt(process.env.MINIO_PORT || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || '',
  secretKey: process.env.MINIO_SECRET_KEY || '',
};

// 创建MinIO客户端实例
export const minioClient = new Minio.Client(minioConfig);

// 默认存储桶名称
export const MINIO_BUCKET = process.env.MINIO_BUCKET || 'xiaoshuo';

/**
 * 确保存储桶存在
 */
export const ensureBucketExists = async (): Promise<void> => {
  try {
    const exists = await minioClient.bucketExists(MINIO_BUCKET);
    if (!exists) {
      await minioClient.makeBucket(MINIO_BUCKET, 'us-east-1');
      console.log(`存储桶 ${MINIO_BUCKET} 创建成功`);
    }
  } catch (error) {
    console.error('检查/创建存储桶失败:', error);
    throw error;
  }
};

/**
 * 生成文件路径
 * @param userEmail 用户邮箱
 * @param novelId 小说ID
 * @param workTitle 作品标题
 * @param fileName 文件名
 * @returns 完整的文件路径
 */
export const generateFilePath = (userEmail: string, novelId: string, workTitle: string, fileName: string = '正文.txt'): string => {
  // 清理文件名中的特殊字符，但保留中文字符
  const cleanEmail = userEmail.replace(/[<>:"/\\|?*]/g, '_');
  const cleanWorkTitle = workTitle.replace(/[<>:"/\\|?*]/g, '_');
  const cleanFileName = fileName.replace(/[<>:"/\\|?*]/g, '_');

  return `xiaoshuo/${cleanEmail}/${novelId}/正文/${cleanWorkTitle}.txt`;
};

/**
 * 上传文件到MinIO
 * @param objectName 对象名称（文件路径）
 * @param fileBuffer 文件内容
 * @param contentType 文件类型
 * @returns 上传结果
 */
export const uploadFile = async (
  objectName: string, 
  fileBuffer: Buffer, 
  contentType: string = 'text/plain; charset=utf-8'
): Promise<Minio.UploadedObjectInfo> => {
  try {
    await ensureBucketExists();
    
    const result = await minioClient.putObject(
      MINIO_BUCKET,
      objectName,
      fileBuffer,
      fileBuffer.length,
      {
        'Content-Type': contentType,
        'Content-Encoding': 'utf-8'
      }
    );
    
    console.log(`文件上传成功: ${objectName}`);
    return result;
  } catch (error) {
    console.error(`文件上传失败: ${objectName}`, error);
    throw error;
  }
};

/**
 * 从MinIO下载文件
 * @param objectName 对象名称（文件路径）
 * @returns 文件流
 */
export const downloadFile = async (objectName: string): Promise<NodeJS.ReadableStream> => {
  try {
    const stream = await minioClient.getObject(MINIO_BUCKET, objectName);
    console.log(`文件下载成功: ${objectName}`);
    return stream;
  } catch (error) {
    console.error(`文件下载失败: ${objectName}`, error);
    throw error;
  }
};

/**
 * 删除MinIO中的文件
 * @param objectName 对象名称（文件路径）
 */
export const deleteFile = async (objectName: string): Promise<void> => {
  try {
    await minioClient.removeObject(MINIO_BUCKET, objectName);
    console.log(`文件删除成功: ${objectName}`);
  } catch (error) {
    console.error(`文件删除失败: ${objectName}`, error);
    throw error;
  }
};

/**
 * 列出用户的所有文件
 * @param userEmail 用户邮箱
 * @returns 文件列表
 */
export const listUserFiles = async (userEmail: string): Promise<Minio.BucketItem[]> => {
  try {
    const cleanEmail = userEmail.replace(/[<>:"/\\|?*]/g, '_');
    const prefix = `xiaoshuo/${cleanEmail}/`;
    
    const files: Minio.BucketItem[] = [];
    const stream = minioClient.listObjects(MINIO_BUCKET, prefix, true);
    
    return new Promise((resolve, reject) => {
      stream.on('data', (obj) => files.push(obj));
      stream.on('error', reject);
      stream.on('end', () => resolve(files));
    });
  } catch (error) {
    console.error(`列出用户文件失败: ${userEmail}`, error);
    throw error;
  }
};
