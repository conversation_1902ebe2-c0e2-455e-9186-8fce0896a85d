/**
 * 文件上传API
 * 处理文件上传到MinIO
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { uploadFile, generateFilePath } from '@/lib/minio';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * 服务器端获取当前用户
 */
async function getCurrentUser(request: NextRequest) {
  try {
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {
            // 在API路由中不需要设置cookies
          },
        },
      }
    );

    const { data: { user }, error } = await supabaseServer.auth.getUser();

    if (error) {
      console.error('获取用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取用户异常:', error);
    return null;
  }
}

/**
 * POST 方法 - 上传文件
 */
export async function POST(request: NextRequest) {
  try {
    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user || !user.email) {
      return NextResponse.json(
        { error: '用户未登录或邮箱不存在' },
        { status: 401 }
      );
    }

    // 解析表单数据
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const workTitle = formData.get('workTitle') as string;

    // 验证必需参数
    if (!file || !workTitle) {
      return NextResponse.json(
        { error: '缺少必需参数：file, workTitle' },
        { status: 400 }
      );
    }

    // 验证文件大小（限制20MB）
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: '文件大小不能超过20MB' },
        { status: 400 }
      );
    }

    // 读取文件内容
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // 创建Supabase客户端用于数据库操作
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {},
        },
      }
    );

    // 检查是否已存在相同作品标题的文件记录
    const { data: existingFile, error: checkError } = await supabaseServer
      .from('novel_files')
      .select('*')
      .eq('user_id', user.id)
      .eq('work_title', workTitle)
      .single();

    let dbResult;
    let dbError;
    let novelId: string;

    if (existingFile && !checkError) {
      // 如果文件已存在，使用现有ID并更新记录
      novelId = existingFile.id;
      console.log(`更新现有文件记录: work_title=${workTitle}, novelId=${novelId}`);

      // 生成新的文件路径
      const filePath = generateFilePath(user.email, novelId, workTitle, file.name);

      console.log(`开始上传文件: ${filePath}`);

      // 上传到MinIO
      const uploadResult = await uploadFile(filePath, fileBuffer, file.type || 'text/plain; charset=utf-8');

      const { data, error } = await supabaseServer
        .from('novel_files')
        .update({
          file_name: file.name,
          file_path: filePath,
          file_size: file.size,
          mime_type: file.type || 'text/plain',
          minio_bucket: 'xiaoshuo',
          minio_object_key: filePath,
          upload_time: new Date().toISOString() // 更新上传时间
        })
        .eq('id', existingFile.id)
        .select()
        .single();

      dbResult = data;
      dbError = error;
    } else {
      // 如果文件不存在，先插入新记录获取ID
      console.log(`创建新文件记录: work_title=${workTitle}`);
      const { data, error } = await supabaseServer
        .from('novel_files')
        .insert({
          user_id: user.id,
          work_title: workTitle,
          file_name: file.name,
          file_path: '', // 临时为空，稍后更新
          file_size: file.size,
          mime_type: file.type || 'text/plain',
          minio_bucket: 'xiaoshuo',
          minio_object_key: '' // 临时为空，稍后更新
        })
        .select()
        .single();

      if (error || !data) {
        console.error('创建数据库记录失败:', error);
        return NextResponse.json(
          { error: '创建数据库记录失败' },
          { status: 500 }
        );
      }

      novelId = data.id;

      // 生成文件路径
      const filePath = generateFilePath(user.email, novelId, workTitle, file.name);

      console.log(`开始上传文件: ${filePath}`);

      // 上传到MinIO
      const uploadResult = await uploadFile(filePath, fileBuffer, file.type || 'text/plain; charset=utf-8');

      // 更新数据库记录中的路径信息
      const { data: updatedData, error: updateError } = await supabaseServer
        .from('novel_files')
        .update({
          file_path: filePath,
          minio_object_key: filePath
        })
        .eq('id', novelId)
        .select()
        .single();

      dbResult = updatedData;
      dbError = updateError;
    }

    if (dbError) {
      console.error('数据库操作失败:', dbError);
      return NextResponse.json(
        { error: '数据库操作失败' },
        { status: 500 }
      );
    }

    console.log(`文件上传成功: ${dbResult.minio_object_key}`);

    return NextResponse.json({
      success: true,
      message: '文件上传成功',
      data: {
        id: dbResult.id,
        filePath: dbResult.minio_object_key,
        fileName: file.name,
        fileSize: file.size,
        uploadTime: dbResult.upload_time,
        workTitle: workTitle
      }
    });

  } catch (error) {
    console.error('文件上传失败:', error);
    return NextResponse.json(
      { 
        error: '文件上传失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
