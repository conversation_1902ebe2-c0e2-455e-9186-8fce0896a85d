/**
 * 验证工具函数
 */

/**
 * 验证标题
 * @param title 标题
 * @returns 错误信息或null
 */
export const validateTitle = (title: string): string | null => {
  if (!title.trim()) {
    return '标题不能为空';
  }

  if (title.length > 100) {
    return '标题不能超过100个字符';
  }

  return null;
};

/**
 * 验证内容
 * @param content 内容
 * @returns 错误信息或null
 */
export const validateContent = (content: string): string | null => {
  if (!content.trim()) {
    return '内容不能为空';
  }

  return null;
};

/**
 * 验证描述
 * @param description 描述
 * @returns 错误信息或null
 */
export const validateDescription = (description: string): string | null => {
  if (description.length > 500) {
    return '描述不能超过500个字符';
  }

  return null;
};

/**
 * 验证API密钥
 * @param apiKey API密钥
 * @returns 错误信息或null
 */
export const validateApiKey = (apiKey: string): string | null => {
  if (!apiKey.trim()) {
    return 'API密钥不能为空';
  }

  // 简单验证API密钥格式
  if (!/^[a-zA-Z0-9_-]{10,}$/.test(apiKey)) {
    return 'API密钥格式不正确';
  }

  return null;
};

/**
 * 验证用户显示名称
 * @param displayName 显示名称
 * @returns 错误信息或null
 */
export const validateDisplayName = (displayName: string): string | null => {
  const trimmed = displayName.trim();

  if (!trimmed) {
    return '用户名不能为空';
  }

  // 只允许中文、英文和数字
  const allowedCharsRegex = /^[\u4e00-\u9fffa-zA-Z0-9]+$/;
  if (!allowedCharsRegex.test(trimmed)) {
    return '用户名只能包含中文、英文和数字，不能包含空格或标点符号';
  }

  // 计算显示长度：中文算2个字符，英文和数字算1个字符
  let displayLength = 0;
  for (let i = 0; i < trimmed.length; i++) {
    const charCode = trimmed.charCodeAt(i);
    if (charCode >= 0x4e00 && charCode <= 0x9fff) { // 中文字符范围
      displayLength += 2;
    } else {
      displayLength += 1;
    }
  }

  if (displayLength > 16) {
    return '用户名过长，最多8个中文字符或16个英文数字字符';
  }

  return null;
};

/**
 * 验证邮箱格式和域名
 * @param email 邮箱
 * @returns 错误信息或null
 */
export const validateEmailDomain = (email: string): string | null => {
  if (!email.trim()) {
    return '邮箱不能为空';
  }

  // 基本邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return '邮箱格式不正确';
  }

  // 域名验证
  const allowedDomains = ['qq.com', '163.com', 'gmail.com'];
  const emailDomain = email.split('@')[1]?.toLowerCase();
  if (!emailDomain || !allowedDomains.includes(emailDomain)) {
    return '只支持 @qq.com、@163.com 和 @gmail.com 邮箱注册';
  }

  return null;
};
